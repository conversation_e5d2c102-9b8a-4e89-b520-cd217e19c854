"use client";
import { useMemo } from "react";
import { useTranslations, useLocale } from "next-intl";
import { BodyText } from "@/components/BodyText";
import { defaultLocale } from "@/config";
import { useRouter } from "@/navigation";

// 产品变体分组配置 - 包含每个产品的属性映射
// 注意：这里的属性名需要与产品实际的属性名完全匹配
const PRODUCT_VARIANT_GROUPS = {
  "nova-fiberglass-pickleball-paddle": [
    {
      slug: "nova-fiberglass-pickleball-paddle-style-meets-performance",
      attributes: {
        "Style": "Style Meets Performance"
        // 可以添加更多属性，如：
        // "Thickness": "13mm",
        // "Color": "Blue",
        // "Weight": "8.2oz"
      }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-kiki",
      attributes: {
        "Style": "Kiki"
        // 同样可以添加其他属性
      }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart-go-pop",
      attributes: {
        "Style": "Meowzart Go Pop"
      }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart",
      attributes: {
        "Style": "Meowzart"
      }
    }
  ]
  // 可以在这里添加更多的产品分组
  // "other-product-series": [
  //   {
  //     slug: "product-1",
  //     attributes: { "Style": "Classic", "Color": "Blue", "Thickness": "13mm" }
  //   },
  //   {
  //     slug: "product-2",
  //     attributes: { "Style": "Modern", "Color": "Red", "Thickness": "16mm" }
  //   }
  // ]
};

type ProductVariantGroupProps = {
  currentSlug: string;
  product?: any; // 当前产品数据，用于获取额外的属性信息
  onVariantSelect?: (slug: string) => void;
};

export default function ProductVariantGroup({
  currentSlug,
  product,
  onVariantSelect
}: ProductVariantGroupProps) {
  const t = useTranslations("nav");
  const locale = useLocale();
  const router = useRouter();

  // 查找当前产品所属的分组
  const currentGroup = useMemo(() => {
    for (const [baseSlug, products] of Object.entries(PRODUCT_VARIANT_GROUPS)) {
      const foundProduct = products.find(p => p.slug === currentSlug);
      if (foundProduct) {
        return {
          baseSlug,
          products,
          currentProduct: foundProduct,
          groupName: baseSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        };
      }
    }
    return null;
  }, [currentSlug]);

  // 如果当前产品不在任何分组中，不显示组件
  if (!currentGroup) {
    return null;
  }

  // 从分组中的所有产品提取属性选项
  const groupAttributes = useMemo(() => {
    const attributeOptions: Record<string, Set<string>> = {};

    // 遍历分组中的所有产品，收集属性
    currentGroup.products.forEach(product => {
      Object.entries(product.attributes).forEach(([attrName, attrValue]) => {
        if (!attributeOptions[attrName]) {
          attributeOptions[attrName] = new Set();
        }
        attributeOptions[attrName].add(attrValue);
      });
    });

    // 如果有当前产品数据，也从中提取属性
    if (product?.variants) {
      product.variants.forEach((variant: any) => {
        variant.attributes?.forEach((attr: any) => {
          const attrName = attr.attribute?.name?.trim();
          const attrValue = attr.values?.[0]?.name || attr.values?.[0]?.value;

          if (attrName && attrValue) {
            if (!attributeOptions[attrName]) {
              attributeOptions[attrName] = new Set();
            }
            attributeOptions[attrName].add(attrValue);
          }
        });
      });
    }

    // 转换为数组格式
    const result: Record<string, string[]> = {};
    Object.entries(attributeOptions).forEach(([key, valueSet]) => {
      result[key] = Array.from(valueSet);
    });

    return result;
  }, [currentGroup, product]);

  // 获取当前产品的属性值
  const currentAttributes = useMemo(() => {
    return currentGroup.currentProduct.attributes;
  }, [currentGroup]);

  // 根据属性组合查找匹配的产品
  const findMatchingProduct = (selectedAttributes: Record<string, string>) => {
    return currentGroup.products.find(product => {
      return Object.entries(selectedAttributes).every(([key, value]) => {
        return product.attributes[key] === value;
      });
    });
  };

  // 处理属性选择
  const handleAttributeSelect = (attributeName: string, value: string) => {
    const newAttributes = {
      ...currentAttributes,
      [attributeName]: value
    };

    // 查找匹配的产品
    const matchingProduct = findMatchingProduct(newAttributes);

    if (matchingProduct && matchingProduct.slug !== currentSlug) {
      // 如果有回调函数，先调用回调
      if (onVariantSelect) {
        onVariantSelect(matchingProduct.slug);
      }

      // 跳转到对应的产品详情页
      router.push(`/product/${matchingProduct.slug}`);
    }
  };

  return (
    <div className="product-variant-group mb-4 space-y-3">
      {Object.entries(groupAttributes).map(([attributeName, options]) => (
        <div key={attributeName} className="attribute-group">
          <BodyText
            intent="bold"
            size="lg"
            className={`!text-[16px] ${locale === defaultLocale ? "ib" : "font-semibold"} uppercase mb-2`}
          >
            {t(attributeName)}:
            <span className="ml-2 normal-case">
              {currentAttributes[attributeName] || ''}
            </span>
          </BodyText>

          <div className="flex flex-wrap gap-2">
            {options.map((option) => {
              const isSelected = currentAttributes[attributeName] === option;
              const isColorAttribute = attributeName.toLowerCase() === 'color';

              // 检查这个选项是否可用（是否有对应的产品）
              const testAttributes = { ...currentAttributes, [attributeName]: option };
              const isAvailable = !!findMatchingProduct(testAttributes);

              const getButtonClasses = () => {
                const baseClasses = 'border-[2px] transition-colors';
                const sizeClasses = isColorAttribute ? 'w-[74px] h-[42px] p-0 overflow-hidden' : 'px-4 py-2';

                if (isColorAttribute) {
                  if (isSelected) return `${baseClasses} ${sizeClasses} border-black`;
                  if (isAvailable) return `${baseClasses} ${sizeClasses} border-gray-300 hover:border-gray-400`;
                  return `${baseClasses} ${sizeClasses} border-gray-200 cursor-not-allowed opacity-50`;
                } else {
                  if (isSelected) return `${baseClasses} ${sizeClasses} bg-black text-white border-black`;
                  if (isAvailable) return `${baseClasses} ${sizeClasses} bg-white text-black border-gray-300 hover:border-gray-400 hover:bg-gray-50`;
                  return `${baseClasses} ${sizeClasses} bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed`;
                }
              };

              return (
                <button
                  key={option}
                  disabled={!isAvailable}
                  onClick={() => isAvailable && handleAttributeSelect(attributeName, option)}
                  className={getButtonClasses()}
                  style={isColorAttribute ? { backgroundColor: option } : {}}
                  title={option}
                >
                  {!isColorAttribute && (
                    <BodyText
                      size="xs"
                      intent="semibold"
                      className="!text-[14px] !font-normal"
                    >
                      {option}
                    </BodyText>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      ))}

      <div className="mt-2 text-xs text-gray-500">
        {currentGroup.products.length} {t('variants')} {t('available')}
      </div>
    </div>
  );
}
