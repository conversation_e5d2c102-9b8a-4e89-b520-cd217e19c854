"use client";
import { useEffect, useMemo, useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import { BodyText } from "@/components/BodyText";
import { defaultLocale } from "@/config";
import { useRouter } from "@/navigation";
import { getColorImagePath } from "@/utils/categoryMapping";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

// 产品变体分组配置 - 每个分组包含产品slug和对应的属性映射
const PRODUCT_VARIANT_GROUPS = {
  "nova-fiberglass-pickleball-paddle": {
    products: [
      {
        slug: "nova-fiberglass-pickleball-paddle-style-meets-performance",
        attributes: {
          "Style": "Style Meets Performance"
        }
      },
      {
        slug: "nova-fiberglass-pickleball-paddle-kiki",
        attributes: {
          "Style": "Kiki"
        }
      },
      {
        slug: "nova-fiberglass-pickleball-paddle-meowzart-go-pop",
        attributes: {
          "Style": "Meowzart Go Pop"
        }
      },
      {
        slug: "nova-fiberglass-pickleball-paddle-meowzart",
        attributes: {
          "Style": "Meowzart"
        }
      }
    ],
    // 定义这个分组支持的属性类型 - 这些属性名需要与产品实际的属性名匹配
    attributeTypes: ["Style", "Thickness", "Color", "Weight", "Core Technology"] // 可以根据实际情况调整
  }
  // 可以在这里添加更多的产品分组
  // 例如：
  // "other-product-series": {
  //   products: [
  //     { slug: "product-1", attributes: { "Style": "Classic", "Color": "Blue" } },
  //     { slug: "product-2", attributes: { "Style": "Modern", "Color": "Red" } }
  //   ],
  //   attributeTypes: ["Style", "Color"]
  // }
};

type ProductVariantGroupProps = {
  currentSlug: string;
  product?: any; // 当前产品数据，用于获取可用的属性选项
  onVariantSelect?: (slug: string) => void;
};

export default function ProductVariantGroup({
  currentSlug,
  product,
  onVariantSelect
}: ProductVariantGroupProps) {
  const t = useTranslations("nav");
  const locale = useLocale();
  const router = useRouter();
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, string>>({});

  // 查找当前产品所属的分组
  const currentGroup = useMemo(() => {
    for (const [baseSlug, groupData] of Object.entries(PRODUCT_VARIANT_GROUPS)) {
      const foundProduct = groupData.products.find(p => p.slug === currentSlug);
      if (foundProduct) {
        return {
          baseSlug,
          groupData,
          currentProduct: foundProduct,
          groupName: baseSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        };
      }
    }
    return null;
  }, [currentSlug]);

  // 如果当前产品不在任何分组中，不显示组件
  if (!currentGroup) {
    return null;
  }

  // 初始化选中的属性（基于当前产品）
  useEffect(() => {
    if (currentGroup.currentProduct) {
      setSelectedAttributes(currentGroup.currentProduct.attributes);
    }
  }, [currentGroup]);

  // 从产品数据中提取所有可用的属性选项
  const availableAttributes = useMemo(() => {
    if (!product?.variants || !currentGroup) return {};

    const attributeOptions: Record<string, string[]> = {};

    // 从当前产品的变体中提取属性选项
    product.variants.forEach((variant: any) => {
      variant.attributes?.forEach((attr: any) => {
        const attrName = attr.attribute?.name?.trim();
        const attrValue = attr.values?.[0]?.name || attr.values?.[0]?.value;

        if (attrName && attrValue && currentGroup.groupData.attributeTypes.includes(attrName)) {
          if (!attributeOptions[attrName]) {
            attributeOptions[attrName] = [];
          }
          if (!attributeOptions[attrName].includes(attrValue)) {
            attributeOptions[attrName].push(attrValue);
          }
        }
      });
    });

    // 同时从分组中的其他产品获取属性选项
    currentGroup.groupData.products.forEach(product => {
      Object.entries(product.attributes).forEach(([attrName, attrValue]) => {
        if (currentGroup.groupData.attributeTypes.includes(attrName)) {
          if (!attributeOptions[attrName]) {
            attributeOptions[attrName] = [];
          }
          if (!attributeOptions[attrName].includes(attrValue)) {
            attributeOptions[attrName].push(attrValue);
          }
        }
      });
    });

    return attributeOptions;
  }, [product, currentGroup]);

  // 根据选中的属性组合查找匹配的产品
  const findMatchingProduct = (attributes: Record<string, string>) => {
    return currentGroup.groupData.products.find(product => {
      return Object.entries(attributes).every(([key, value]) => {
        return product.attributes[key] === value;
      });
    });
  };

  // 处理属性选择
  const handleAttributeSelect = (attributeName: string, value: string) => {
    const newAttributes = {
      ...selectedAttributes,
      [attributeName]: value
    };

    setSelectedAttributes(newAttributes);

    // 查找匹配的产品
    const matchingProduct = findMatchingProduct(newAttributes);

    if (matchingProduct && matchingProduct.slug !== currentSlug) {
      // 如果有回调函数，先调用回调
      if (onVariantSelect) {
        onVariantSelect(matchingProduct.slug);
      }

      // 跳转到对应的产品详情页
      router.push(`/product/${matchingProduct.slug}`);
    }
  };

  return (
    <div className="product-variant-group mb-4 space-y-3">
      {Object.entries(availableAttributes).map(([attributeName, options]) => (
        <div key={attributeName} className="attribute-group">
          <BodyText
            intent="bold"
            size="lg"
            className={`!text-[16px] ${locale === defaultLocale ? "ib" : "font-semibold"} uppercase mb-2`}
          >
            {t(attributeName)}:
            <span className="ml-2 normal-case">
              {selectedAttributes[attributeName] || ''}
            </span>
          </BodyText>

          <div className="flex flex-wrap gap-2">
            {options.map((option) => {
              const isSelected = selectedAttributes[attributeName] === option;
              const isColorAttribute = attributeName.toLowerCase() === 'color';

              // 检查这个选项是否可用（是否有对应的产品）
              const testAttributes = { ...selectedAttributes, [attributeName]: option };
              const isAvailable = !!findMatchingProduct(testAttributes);

              const getButtonClasses = () => {
                const baseClasses = 'border-[2px] transition-colors';
                const sizeClasses = isColorAttribute ? 'w-[74px] h-[42px] p-0 overflow-hidden' : 'px-4 py-2';

                if (isColorAttribute) {
                  if (isSelected) return `${baseClasses} ${sizeClasses} border-black`;
                  if (isAvailable) return `${baseClasses} ${sizeClasses} border-gray-300 hover:border-gray-400`;
                  return `${baseClasses} ${sizeClasses} border-gray-200 cursor-not-allowed opacity-50`;
                } else {
                  if (isSelected) return `${baseClasses} ${sizeClasses} bg-black text-white border-black`;
                  if (isAvailable) return `${baseClasses} ${sizeClasses} bg-white text-black border-gray-300 hover:border-gray-400 hover:bg-gray-50`;
                  return `${baseClasses} ${sizeClasses} bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed`;
                }
              };

              return (
                <button
                  key={option}
                  disabled={!isAvailable}
                  onClick={() => isAvailable && handleAttributeSelect(attributeName, option)}
                  className={getButtonClasses()}
                  style={isColorAttribute ? { backgroundColor: option } : {}}
                  title={option}
                >
                  {!isColorAttribute && (
                    <BodyText
                      size="xs"
                      intent="semibold"
                      className="!text-[14px] !font-normal"
                    >
                      {option}
                    </BodyText>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      ))}

      <div className="mt-2 text-xs text-gray-500">
        {currentGroup.groupData.products.length} {t('variants')} {t('available')}
      </div>
    </div>
  );
}
