"use client";
import { useEffect, useMemo, useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import { BodyText } from "@/components/BodyText";
import { defaultLocale } from "@/config";
import { useRouter } from "@/navigation";

// 产品变体分组配置
const PRODUCT_VARIANT_GROUPS = {
  "nova-fiberglass-pickleball-paddle": [
    "nova-fiberglass-pickleball-paddle-style-meets-performance",
    "nova-fiberglass-pickleball-paddle-kiki", 
    "nova-fiberglass-pickleball-paddle-meowzart-go-pop",
    "nova-fiberglass-pickleball-paddle-meowzart"
  ]
  // 可以在这里添加更多的产品分组
  // "other-product-base": ["variant1", "variant2", "variant3"]
};

type ProductVariantGroupProps = {
  currentSlug: string;
  onVariantSelect?: (slug: string) => void;
};

export default function ProductVariantGroup({ 
  currentSlug, 
  onVariantSelect 
}: ProductVariantGroupProps) {
  const t = useTranslations("nav");
  const locale = useLocale();
  const router = useRouter();
  const [selectedSlug, setSelectedSlug] = useState<string>(currentSlug);

  // 查找当前产品所属的分组
  const currentGroup = useMemo(() => {
    for (const [baseSlug, variants] of Object.entries(PRODUCT_VARIANT_GROUPS)) {
      if (variants.includes(currentSlug)) {
        return {
          baseSlug,
          variants,
          groupName: baseSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        };
      }
    }
    return null;
  }, [currentSlug]);

  // 如果当前产品不在任何分组中，不显示组件
  if (!currentGroup) {
    return null;
  }

  // 处理变体选择
  const handleVariantSelect = (slug: string) => {
    if (slug === currentSlug) return; // 如果选择的是当前产品，不做任何操作
    
    setSelectedSlug(slug);
    
    // 如果有回调函数，先调用回调
    if (onVariantSelect) {
      onVariantSelect(slug);
    }
    
    // 跳转到对应的产品详情页
    router.push(`/product/${slug}`);
  };

  // 格式化显示名称
  const formatDisplayName = (slug: string) => {
    // 移除基础slug部分，只保留变体特有的部分
    const basePart = currentGroup.baseSlug;
    let displayName = slug.replace(basePart, '').replace(/^-/, '');
    
    // 如果没有特殊部分，使用完整的slug
    if (!displayName) {
      displayName = slug;
    }
    
    // 格式化显示名称：替换连字符为空格，首字母大写
    return displayName
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim() || 'Default';
  };

  return (
    <div className="product-variant-group mb-4">
      <BodyText 
        intent="bold" 
        size="lg" 
        className={`!text-[16px] ${locale === defaultLocale ? "ib" : "font-semibold"} uppercase mb-2`}
      >
        {t('Style')}:
        <span className="ml-2 normal-case">
          {formatDisplayName(selectedSlug)}
        </span>
      </BodyText>
      
      <div className="flex flex-wrap gap-2">
        {currentGroup.variants.map((slug) => {
          const isSelected = slug === selectedSlug;
          const displayName = formatDisplayName(slug);
          
          return (
            <button
              key={slug}
              onClick={() => handleVariantSelect(slug)}
              className={`
                px-4 py-2 border-[2px] transition-colors cursor-pointer
                ${isSelected 
                  ? 'bg-black text-white border-black' 
                  : 'bg-white text-black border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                }
              `}
              title={displayName}
            >
              <BodyText
                size="xs"
                intent="semibold"
                className="!text-[14px] !font-normal"
              >
                {displayName}
              </BodyText>
            </button>
          );
        })}
      </div>
      
      <div className="mt-2 text-xs text-gray-500">
        {currentGroup.variants.length} {t('variants')} {t('available')}
      </div>
    </div>
  );
}
