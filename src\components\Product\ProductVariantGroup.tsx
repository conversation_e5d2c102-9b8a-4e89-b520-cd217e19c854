"use client";
import { useMemo } from "react";
import { useTranslations, useLocale } from "next-intl";
import { BodyText } from "@/components/BodyText";
import { defaultLocale } from "@/config";
import { useRouter } from "@/navigation";

// 产品变体分组配置 - 简化版本，专门用于产品系列切换
const PRODUCT_VARIANT_GROUPS = {
  "nova-fiberglass-pickleball-paddle": [
    {
      slug: "nova-fiberglass-pickleball-paddle-style-meets-performance",
      name: "Style Meets Performance"
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-kiki",
      name: "<PERSON><PERSON>"
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart-go-pop",
      name: "Meowzart Go Pop"
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart",
      name: "Meowzart"
    }
  ]
  // 可以在这里添加更多的产品分组
  // "other-product-series": [
  //   { slug: "product-1", name: "Product 1" },
  //   { slug: "product-2", name: "Product 2" }
  // ]
};

type ProductVariantGroupProps = {
  currentSlug: string;
  onVariantSelect?: (slug: string) => void;
};

export default function ProductVariantGroup({
  currentSlug,
  onVariantSelect
}: ProductVariantGroupProps) {
  const t = useTranslations("nav");
  const locale = useLocale();
  const router = useRouter();

  // 查找当前产品所属的分组
  const currentGroup = useMemo(() => {
    for (const [baseSlug, products] of Object.entries(PRODUCT_VARIANT_GROUPS)) {
      const foundProduct = products.find(p => p.slug === currentSlug);
      if (foundProduct) {
        return {
          baseSlug,
          products,
          currentProduct: foundProduct,
          groupName: baseSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        };
      }
    }
    return null;
  }, [currentSlug]);

  // 如果当前产品不在任何分组中，不显示组件
  if (!currentGroup) {
    return null;
  }

  // 处理产品切换
  const handleProductSelect = (slug: string) => {
    if (slug === currentSlug) return; // 如果选择的是当前产品，不做任何操作

    // 如果有回调函数，先调用回调
    if (onVariantSelect) {
      onVariantSelect(slug);
    }

    // 跳转到对应的产品详情页
    router.push(`/product/${slug}`);
  };

  return (
    <div className="product-variant-group mb-4">
      <BodyText
        intent="bold"
        size="lg"
        className={`!text-[16px] ${locale === defaultLocale ? "ib" : "font-semibold"} uppercase mb-2`}
      >
        {t('Style')}:
        <span className="ml-2 normal-case">
          {currentGroup.currentProduct.name}
        </span>
      </BodyText>

      <div className="flex flex-wrap gap-2">
        {currentGroup.products.map((product) => {
          const isSelected = product.slug === currentSlug;

          return (
            <button
              key={product.slug}
              onClick={() => handleProductSelect(product.slug)}
              className={`
                px-4 py-2 border-[2px] transition-colors cursor-pointer
                ${isSelected
                  ? 'bg-black text-white border-black'
                  : 'bg-white text-black border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                }
              `}
              title={product.name}
            >
              <BodyText
                size="xs"
                intent="semibold"
                className="!text-[14px] !font-normal"
              >
                {product.name}
              </BodyText>
            </button>
          );
        })}
      </div>

      <div className="mt-2 text-xs text-gray-500">
        {currentGroup.products.length} {t('variants')} {t('available')}
      </div>
    </div>
  );
}
