"use client";
import { useEffect, useMemo, useState } from "react";
import { useTranslations,useLocale } from "next-intl";
import { BodyText } from "@/components/BodyText";
import Skeleton from "react-loading-skeleton";
import { defaultLocale } from "@/config";
import { getColorImagePath } from "@/utils/categoryMapping";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import ProductVariantGroup from "./ProductVariantGroup";
type AttributeSelectorProps = {
  variants: any[];
  onChange: (variantId: string) => void;
  changeValue: (params: any) => void;
  product?: any; // 添加产品信息，用于获取分类
  currentSlug?: string; // 添加当前产品的slug
};

export default function AttributeSelector({ variants, onChange, changeValue, product, currentSlug }: AttributeSelectorProps) {
  const t = useTranslations("nav");
  const locale = useLocale();
  const [selectedValues, setSelectedValues] = useState<Record<string, string>>({});
  console.log("product====",product);

  const attributeGroups = useMemo(() => {
    if (!variants?.length) return [];

    const attributeMap = new Map<string, any>();

    variants.forEach(variant => {
      variant.attributes?.forEach((attr: any) => {
        // 修复1：统一属性名大小写和空格处理
        const attrName = attr.attribute?.name?.trim();

        const excludedAttributes = ['cost', 'cost price', 'costprice']; // 需要排除的属性名列表
        if (!attrName || excludedAttributes.includes(attrName.trim())) return;

        // 修复2：兼容不同值类型
        const attrValue = attr.values?.[0]?.name || attr.values?.[0]?.value;

        if (!attrValue) return;

        // 修复3：处理特殊符号值（如13*14） 只删除首尾空格，保留内部空格
        const normalizedValue = String(attrValue).trim();

        if (!attributeMap.has(attrName)) {
          attributeMap.set(attrName, {
            attribute: attr.attribute,
            values: new Set(),
            variants: new Set()
          });
        }

        const group = attributeMap.get(attrName)!;
        group.values.add(normalizedValue);  // 存储标准化后的值
        group.variants.add(variant.id);
      });
    });

    return Array.from(attributeMap).map(([name, data]) => {
      // 对值进行排序
      // const sortedValues = Array.from(data.values).sort((a, b) => {
      //   // 检查是否为数字
      //   const numA = parseFloat(a);
      //   const numB = parseFloat(b);
        
      //   if (!isNaN(numA) && !isNaN(numB)) {
      //     // 如果是数字，按数字大小排序
      //     return numA - numB;
      //   } else {
      //     // 如果不是数字，按字符串排序
      //     return a.localeCompare(b);
      //   }
      // });

      return {
        name: data.attribute.name,
        attribute: data.attribute,
        values: Array.from(data.values),
        // values: sortedValues,
        variants: Array.from(data.variants)
      };
    });
  }, [variants]);

  const availableValues = useMemo(() => {
    const result: Record<string, Array<{ value: string; available: boolean }>> = {};

    attributeGroups.forEach(group => {
      const normalizedGroupName = group.attribute.name.trim();
      // @ts-ignore
      result[group.name] = group.values.map(value => {
        const combination = {
          ...Object.fromEntries(
            Object.entries(selectedValues).map(([k, v]) => [k.trim(), v])
          ),
          [normalizedGroupName]: value
        };

        const exists = variants.some(variant =>
          variant.attributes.every(attr => {
            const attrName = attr.attribute.name.trim()

            // 新增：在匹配时也过滤排除属性
            if (['cost', 'cost price', 'costprice'].includes(attrName)) return true;

            const selectedValue = combination[attrName];
            const normalizedVariantValue = String(attr.values[0]?.name || attr.values[0]?.value).trim();

            return !selectedValue || selectedValue === normalizedVariantValue;
          })
        );

        return { value, available: exists };
      });
    });

    return result;
  }, [selectedValues, attributeGroups, variants]);
  useEffect(() => {
    console.log(variants);
    console.log("selectedValues",selectedValues);
    
    const matchedVariant = variants.find(variant =>
      variant.attributes.every((attr: any) => {
        // 排除不需要匹配的属性
        const attrName = attr.attribute?.name?.trim();
        // 排除这三个属性不参与匹配
        if (['cost', 'cost price', 'costprice'].includes(attrName)) {
          return true; // 直接返回true，表示这个属性总是匹配
        }
        return selectedValues[attr.attribute.name] === attr.values?.[0]?.name
      })
    );
    // 始终触发回调，无匹配时传null
    onChange(matchedVariant?.id || null);
  }, [selectedValues, variants, onChange]);

  // 修复初始化逻辑
  useEffect(() => {
    if (variants?.length > 0 && Object.keys(selectedValues).length === 0) {
      const initialValues: Record<string, string> = {};
      variants[0].attributes?.forEach((attr: any) => {
        // 统一使用标准化属性名
        const attrName = attr.attribute?.name?.trim();
        const value = attr.values?.[0]?.name || attr.values?.[0]?.value;
        if (attrName && value) {
          initialValues[attrName] = String(value).trim();
        }
      });
      setSelectedValues(initialValues);
    }
  }, [variants]);
  // -----------------------------------属性都是空的情况start
  const [itemValue, setItemValue] = useState<any>();
  const [isVariantsLoading, setIsVariantsLoading] = useState(true);
  const [sortedVariants, setSortedVariants] = useState<any[]>([]);
  
  // 处理变体排序的函数
  useEffect(() => {
    if (variants?.length > 0) {
      setIsVariantsLoading(true);
      setSortedVariants([...variants]);
      setTimeout(() => {
        setIsVariantsLoading(false);
      }, 100);
    }
  }, [variants]);
  
  const handleOnchange = (value: string) => {
    console.log("value",value);
    
    setItemValue(value);
    changeValue(value);
  };
  
  useEffect(() => {
    if (sortedVariants.length > 0) {
      setItemValue(sortedVariants[0]);
      changeValue(sortedVariants[0]);
    }
  }, [sortedVariants]);
  // -----------------------------------属性都是空的情况end
  return (
    <div className="space-y-2">
      {/* 产品变体分组选择器 */}
      {currentSlug && (
        <ProductVariantGroup
          currentSlug={currentSlug}
          onVariantSelect={(slug) => {
            // 可以在这里添加额外的逻辑，比如更新状态等
            console.log('Selected variant slug:', slug);
          }}
        />
      )}
      {/* 当所有变体的attributes都为空时显示（过滤排除属性后） */}
      {variants?.length > 0 && variants.every(v => {
        const filteredAttributes = v.attributes?.filter(attr => {
          const attrName = attr.attribute?.name?.trim().toLowerCase();
          return !['cost', 'cost price', 'costprice'].includes(attrName);
        });
        return filteredAttributes?.every(attr =>
          !attr.values?.length ||
          attr.values.every(val => !val.name && !val.value)
        );
      }) ? (
        <>
          {variants ? (
            <BodyText intent={"bold"} size="lg" className="!text-[16px] irs">{t('Variants')}:<span className="ml-2">{itemValue?.name}</span></BodyText>
          ) : (
            <Skeleton width={120} height={28} />
          )}
          <div className="mt-3 flex flex-wrap items-center gap-2.5">
            {isVariantsLoading ? (
              // 显示加载状态
              Array(variants.length).fill(0).map((_, index) => (
                <div key={index} className="h-10 w-32 bg-gray-200"></div>
              ))
            ) : (
              // 显示排序后的变体
              sortedVariants.map((item: any, index: number) => (
                <div
                  key={index}
                  className="cursor-pointer"
                  onClick={() => {
                    handleOnchange(item);
                  }}
                >
                  <BodyText
                    size="xs"
                    intent="semibold"
                    className={`mt-1 ${itemValue == item
                      ? "border-black !bg-black text-white"
                      : "border-[#DFDFDF] !bg-white text-black"
                      } font-abeezee cursor-pointer border-[1px] bg-white px-4 py-2 !text-[16px] !font-normal`}
                  >
                    {item.name}
                  </BodyText>
                </div>
              ))
            )}
          </div>
        </>
      ) : (
        attributeGroups.map(group => (
          <div key={group.name} className="attribute-group">
            <h3 className={`${locale === defaultLocale ? "ib" : "font-semibold"} uppercase mb-2 text-[16px]`}>
              {group.attribute.translation?.name || group.attribute.name}
              {/* 添加当前选中值展示 */}
              {/* {selectedValues[group.attribute.name.trim()] && (
                <span className="ml-2 irs">
                  {selectedValues[group.attribute.name.trim()]}
                </span>
              )} */}
            </h3>
            <div className="flex flex-wrap gap-2">
              {availableValues[group.name].map(({ value, available }) => {
                // 检查是否为颜色属性
                const isColorAttribute = group.attribute.name.toLowerCase().trim() === 'color';

                // 获取颜色对应的图片路径
                const colorImagePath = isColorAttribute ? getColorImagePath(value, product?.category?.slug) : null;

                // 计算按钮样式
                const getButtonClasses = () => {
                  const baseClasses = 'border-[2px] transition-colors';
                  const sizeClasses = isColorAttribute ? 'w-[74px] h-[42px] p-0 overflow-hidden' : 'px-4 py-2';

                  const isSelected = selectedValues[group.name] === value;

                  if (isColorAttribute) {
                    if (isSelected) return `${baseClasses} ${sizeClasses} border-black`;
                    if (available) return `${baseClasses} ${sizeClasses} border-gray-300 hover:border-gray-400`;
                    return `${baseClasses} ${sizeClasses} border-gray-200 cursor-not-allowed opacity-50`;
                  } else {
                    if (isSelected) return `${baseClasses} ${sizeClasses} bg-white text-black border-black`;
                    if (available) return `${baseClasses} ${sizeClasses} bg-white border-gray-300 hover:bg-gray-100`;
                    return `${baseClasses} ${sizeClasses} bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed`;
                  }
                };

                // 渲染按钮内容
                const renderContent = () => {
                  if (!isColorAttribute) return value;

                  if (colorImagePath) {
                    return (
                      <SEOOptimizedImage
                        src={colorImagePath}
                        alt={`RC ${value} color`}
                        width={74}
                        height={42}
                        className="w-full h-full object-cover"
                        unoptimized
                        quality={100}
                        priority
                      />
                    );
                  }

                  return null; // 颜色属性但没有图片时，通过 style 显示背景色
                };

                return (
                  <button
                    key={value}
                    disabled={!available}
                    className={getButtonClasses()}
                    style={isColorAttribute && !colorImagePath ? { backgroundColor: value } : {}}
                    onClick={() => {
                      if (available) {
                        setSelectedValues(prev => {
                          const normalizedName = group.attribute.name.trim();
                          const newValue = prev[normalizedName] === value ? undefined : value;
                          return { ...prev, [normalizedName]: newValue };
                        });
                      }
                    }}
                    title={isColorAttribute ? value : undefined}
                  >
                    {renderContent()}
                  </button>
                );
              })}
            </div>
          </div>
        ))
      )}
    </div>
  );
}