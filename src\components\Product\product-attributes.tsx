"use client";
import { useEffect, useMemo, useState } from "react";
import { useTranslations,useLocale } from "next-intl";
import { BodyText } from "@/components/BodyText";
import Skeleton from "react-loading-skeleton";
import { defaultLocale } from "@/config";
import { getColorImagePath } from "@/utils/categoryMapping";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useRouter } from "@/navigation";

// 产品变体分组配置
const PRODUCT_VARIANT_GROUPS = {
  "nova-fiberglass-pickleball-paddle": [
    {
      slug: "nova-fiberglass-pickleball-paddle-style-meets-performance",
      attributes: {
        "Style": "Style Meets Performance"
      }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-kiki",
      attributes: {
        "Style": "Kiki"
      }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart-go-pop",
      attributes: {
        "Style": "Meowzart Go Pop"
      }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart",
      attributes: {
        "Style": "Meowzart"
      }
    }
  ]
};

type AttributeSelectorProps = {
  variants: any[];
  onChange: (variantId: string) => void;
  changeValue: (params: any) => void;
  product?: any; // 添加产品信息，用于获取分类
  currentSlug?: string; // 添加当前产品的slug
};

export default function AttributeSelector({ variants, onChange, changeValue, product, currentSlug }: AttributeSelectorProps) {
  const t = useTranslations("nav");
  const locale = useLocale();
  const router = useRouter();
  const [selectedValues, setSelectedValues] = useState<Record<string, string>>({});
  console.log("product====",product);

  // 查找当前产品所属的变体分组
  const currentVariantGroup = useMemo(() => {
    if (!currentSlug) return null;

    for (const [baseSlug, products] of Object.entries(PRODUCT_VARIANT_GROUPS)) {
      const foundProduct = products.find(p => p.slug === currentSlug);
      if (foundProduct) {
        return {
          baseSlug,
          products,
          currentProduct: foundProduct
        };
      }
    }
    return null;
  }, [currentSlug]);

  const attributeGroups = useMemo(() => {
    if (!variants?.length) return [];

    const attributeMap = new Map<string, any>();

    // 首先处理当前产品的变体属性
    variants.forEach(variant => {
      variant.attributes?.forEach((attr: any) => {
        // 修复1：统一属性名大小写和空格处理
        const attrName = attr.attribute?.name?.trim();

        const excludedAttributes = ['cost', 'cost price', 'costprice']; // 需要排除的属性名列表
        if (!attrName || excludedAttributes.includes(attrName.trim())) return;

        // 修复2：兼容不同值类型
        const attrValue = attr.values?.[0]?.name || attr.values?.[0]?.value;

        if (!attrValue) return;

        // 修复3：处理特殊符号值（如13*14） 只删除首尾空格，保留内部空格
        const normalizedValue = String(attrValue).trim();

        if (!attributeMap.has(attrName)) {
          attributeMap.set(attrName, {
            attribute: attr.attribute,
            values: new Set(),
            variants: new Set(),
            isVariantGroup: false
          });
        }

        const group = attributeMap.get(attrName)!;
        group.values.add(normalizedValue);  // 存储标准化后的值
        group.variants.add(variant.id);
      });
    });

    // 如果当前产品属于变体分组，将分组中其他产品的属性值融合到现有属性组中
    if (currentVariantGroup) {
      currentVariantGroup.products.forEach(product => {
        Object.entries(product.attributes).forEach(([attrName, attrValue]) => {
          // 查找是否已经存在相同名称的属性组
          const existingGroup = attributeMap.get(attrName);
          if (existingGroup) {
            // 如果属性组已存在，添加新的值并标记为变体分组
            existingGroup.values.add(attrValue);
            existingGroup.isVariantGroup = true;
          } else {
            // 如果属性组不存在，创建新的属性组（这种情况下是纯变体分组属性）
            attributeMap.set(attrName, {
              attribute: {
                name: attrName,
                translation: { name: attrName }
              },
              values: new Set([attrValue]),
              variants: new Set(),
              isVariantGroup: true
            });
          }
        });
      });
    }

    return Array.from(attributeMap).map(([name, data]) => {
      return {
        name: data.attribute.name,
        attribute: data.attribute,
        values: Array.from(data.values),
        variants: Array.from(data.variants),
        isVariantGroup: data.isVariantGroup
      };
    });
  }, [variants, currentVariantGroup]);

  const availableValues = useMemo(() => {
    const result: Record<string, Array<{ value: string; available: boolean }>> = {};

    attributeGroups.forEach(group => {
      const normalizedGroupName = group.attribute.name.trim();
      // @ts-ignore
      result[group.name] = group.values.map(value => {
        // 首先检查是否是当前产品变体中的值
        const combination = {
          ...Object.fromEntries(
            Object.entries(selectedValues).map(([k, v]) => [k.trim(), v])
          ),
          [normalizedGroupName]: value
        };

        const existsInCurrentVariants = variants.some(variant =>
          variant.attributes.every(attr => {
            const attrName = attr.attribute.name.trim()

            // 新增：在匹配时也过滤排除属性
            if (['cost', 'cost price', 'costprice'].includes(attrName)) return true;

            const selectedValue = combination[attrName];
            const normalizedVariantValue = String(attr.values[0]?.name || attr.values[0]?.value).trim();

            return !selectedValue || selectedValue === normalizedVariantValue;
          })
        );

        // 如果在当前变体中存在，直接返回可用
        if (existsInCurrentVariants) {
          return { value, available: true };
        }

        // 如果不在当前变体中，但属性组被标记为变体分组，检查是否有对应的产品
        if (group.isVariantGroup && currentVariantGroup) {
          const hasMatchingProduct = currentVariantGroup.products.some(product =>
            product.attributes[normalizedGroupName] === value
          );
          return { value, available: !!hasMatchingProduct };
        }

        // 否则不可用
        return { value, available: false };
      });
    });

    return result;
  }, [selectedValues, attributeGroups, variants, currentVariantGroup]);
  useEffect(() => {
    console.log(variants);
    console.log("selectedValues",selectedValues);
    
    const matchedVariant = variants.find(variant =>
      variant.attributes.every((attr: any) => {
        // 排除不需要匹配的属性
        const attrName = attr.attribute?.name?.trim();
        // 排除这三个属性不参与匹配
        if (['cost', 'cost price', 'costprice'].includes(attrName)) {
          return true; // 直接返回true，表示这个属性总是匹配
        }
        return selectedValues[attr.attribute.name] === attr.values?.[0]?.name
      })
    );
    // 始终触发回调，无匹配时传null
    onChange(matchedVariant?.id || null);
  }, [selectedValues, variants, onChange]);

  // 修复初始化逻辑
  useEffect(() => {
    if (variants?.length > 0 && Object.keys(selectedValues).length === 0) {
      const initialValues: Record<string, string> = {};
      variants[0].attributes?.forEach((attr: any) => {
        // 统一使用标准化属性名
        const attrName = attr.attribute?.name?.trim();
        const value = attr.values?.[0]?.name || attr.values?.[0]?.value;
        if (attrName && value) {
          initialValues[attrName] = String(value).trim();
        }
      });
      setSelectedValues(initialValues);
    }
  }, [variants]);
  // -----------------------------------属性都是空的情况start
  const [itemValue, setItemValue] = useState<any>();
  const [isVariantsLoading, setIsVariantsLoading] = useState(true);
  const [sortedVariants, setSortedVariants] = useState<any[]>([]);
  
  // 处理变体排序的函数
  useEffect(() => {
    if (variants?.length > 0) {
      setIsVariantsLoading(true);
      setSortedVariants([...variants]);
      setTimeout(() => {
        setIsVariantsLoading(false);
      }, 100);
    }
  }, [variants]);
  
  const handleOnchange = (value: string) => {
    console.log("value",value);
    
    setItemValue(value);
    changeValue(value);
  };
  
  useEffect(() => {
    if (sortedVariants.length > 0) {
      setItemValue(sortedVariants[0]);
      changeValue(sortedVariants[0]);
    }
  }, [sortedVariants]);
  // -----------------------------------属性都是空的情况end
  return (
    <div className="space-y-2">
      {/* 当所有变体的attributes都为空时显示（过滤排除属性后） */}
      {variants?.length > 0 && variants.every(v => {
        const filteredAttributes = v.attributes?.filter(attr => {
          const attrName = attr.attribute?.name?.trim().toLowerCase();
          return !['cost', 'cost price', 'costprice'].includes(attrName);
        });
        return filteredAttributes?.every(attr =>
          !attr.values?.length ||
          attr.values.every(val => !val.name && !val.value)
        );
      }) ? (
        <>
          {variants ? (
            <BodyText intent={"bold"} size="lg" className="!text-[16px] irs">{t('Variants')}:<span className="ml-2">{itemValue?.name}</span></BodyText>
          ) : (
            <Skeleton width={120} height={28} />
          )}
          <div className="mt-3 flex flex-wrap items-center gap-2.5">
            {isVariantsLoading ? (
              // 显示加载状态
              Array(variants.length).fill(0).map((_, index) => (
                <div key={index} className="h-10 w-32 bg-gray-200"></div>
              ))
            ) : (
              // 显示排序后的变体
              sortedVariants.map((item: any, index: number) => (
                <div
                  key={index}
                  className="cursor-pointer"
                  onClick={() => {
                    handleOnchange(item);
                  }}
                >
                  <BodyText
                    size="xs"
                    intent="semibold"
                    className={`mt-1 ${itemValue == item
                      ? "border-black !bg-black text-white"
                      : "border-[#DFDFDF] !bg-white text-black"
                      } font-abeezee cursor-pointer border-[1px] bg-white px-4 py-2 !text-[16px] !font-normal`}
                  >
                    {item.name}
                  </BodyText>
                </div>
              ))
            )}
          </div>
        </>
      ) : (
        attributeGroups.map(group => (
          <div key={group.name} className="attribute-group">
            <h3 className={`${locale === defaultLocale ? "ib" : "font-semibold"} uppercase mb-2 text-[16px]`}>
              {group.attribute.translation?.name || group.attribute.name}
              {/* 添加当前选中值展示 */}
              {/* {selectedValues[group.attribute.name.trim()] && (
                <span className="ml-2 irs">
                  {selectedValues[group.attribute.name.trim()]}
                </span>
              )} */}
            </h3>
            <div className="flex flex-wrap gap-2">
              {availableValues[group.name].map(({ value, available }) => {
                // 检查是否为颜色属性
                const isColorAttribute = group.attribute.name.toLowerCase().trim() === 'color';

                // 获取颜色对应的图片路径
                const colorImagePath = isColorAttribute ? getColorImagePath(value, product?.category?.slug) : null;

                // 计算按钮样式
                const getButtonClasses = () => {
                  const baseClasses = 'border-[2px] transition-colors';
                  const sizeClasses = isColorAttribute ? 'w-[74px] h-[42px] p-0 overflow-hidden' : 'px-4 py-2';

                  const isSelected = selectedValues[group.name] === value;

                  if (isColorAttribute) {
                    if (isSelected) return `${baseClasses} ${sizeClasses} border-black`;
                    if (available) return `${baseClasses} ${sizeClasses} border-gray-300 hover:border-gray-400`;
                    return `${baseClasses} ${sizeClasses} border-gray-200 cursor-not-allowed opacity-50`;
                  } else {
                    if (isSelected) return `${baseClasses} ${sizeClasses} bg-white text-black border-black`;
                    if (available) return `${baseClasses} ${sizeClasses} bg-white border-gray-300 hover:bg-gray-100`;
                    return `${baseClasses} ${sizeClasses} bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed`;
                  }
                };

                // 渲染按钮内容
                const renderContent = () => {
                  if (!isColorAttribute) return value;

                  if (colorImagePath) {
                    return (
                      <SEOOptimizedImage
                        src={colorImagePath}
                        alt={`RC ${value} color`}
                        width={74}
                        height={42}
                        className="w-full h-full object-cover"
                        unoptimized
                        quality={100}
                        priority
                      />
                    );
                  }

                  return null; // 颜色属性但没有图片时，通过 style 显示背景色
                };

                return (
                  <button
                    key={value}
                    disabled={!available}
                    className={getButtonClasses()}
                    style={isColorAttribute && !colorImagePath ? { backgroundColor: value } : {}}
                    onClick={() => {
                      if (available) {
                        // 首先检查这个值是否在当前产品的变体中存在
                        const combination = {
                          ...Object.fromEntries(
                            Object.entries(selectedValues).map(([k, v]) => [k.trim(), v])
                          ),
                          [group.attribute.name.trim()]: value
                        };

                        const existsInCurrentVariants = variants.some(variant =>
                          variant.attributes.every((attr: any) => {
                            const attrName = attr.attribute.name.trim();
                            if (['cost', 'cost price', 'costprice'].includes(attrName)) return true;
                            const selectedValue = combination[attrName];
                            const normalizedVariantValue = String(attr.values[0]?.name || attr.values[0]?.value).trim();
                            return !selectedValue || selectedValue === normalizedVariantValue;
                          })
                        );

                        if (existsInCurrentVariants) {
                          // 如果在当前变体中存在，使用普通属性选择逻辑
                          setSelectedValues(prev => {
                            const normalizedName = group.attribute.name.trim();
                            const newValue = prev[normalizedName] === value ? undefined : value;
                            return { ...prev, [normalizedName]: newValue };
                          });
                        } else if (group.isVariantGroup && currentVariantGroup) {
                          // 如果不在当前变体中，但是变体分组属性，跳转到对应产品
                          const matchingProduct = currentVariantGroup.products.find(product =>
                            product.attributes[group.attribute.name.trim()] === value
                          );
                          if (matchingProduct && matchingProduct.slug !== currentSlug) {
                            router.push(`/product/${matchingProduct.slug}`);
                          }
                        }
                      }
                    }}
                    title={isColorAttribute ? value : undefined}
                  >
                    {renderContent()}
                  </button>
                );
              })}
            </div>
          </div>
        ))
      )}
    </div>
  );
}