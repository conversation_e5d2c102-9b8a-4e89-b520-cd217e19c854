# ProductVariantGroup 组件使用说明

## 功能描述

ProductVariantGroup 组件用于实现基于 slug 的产品变体归类和跳转功能。用户可以通过选择不同的属性组合来跳转到对应的产品详情页。

## 主要特性

1. **基于 slug 的产品分组**: 将相关的产品按照 slug 进行分组管理
2. **属性选择界面**: 根据产品的实际属性动态生成选择界面
3. **智能匹配**: 根据用户选择的属性组合自动匹配对应的产品
4. **自动跳转**: 选择完整的属性组合后自动跳转到对应的产品详情页
5. **可用性检查**: 只显示有对应产品的属性组合

## 配置方法

### 1. 在 ProductVariantGroup.tsx 中配置产品分组

```typescript
const PRODUCT_VARIANT_GROUPS = {
  "产品系列基础名称": {
    products: [
      {
        slug: "完整的产品slug",
        attributes: {
          "属性名1": "属性值1",
          "属性名2": "属性值2"
        }
      },
      // 更多产品...
    ],
    attributeTypes: ["属性名1", "属性名2", "属性名3"] // 需要显示的属性类型
  }
}
```

### 2. 当前配置示例

```typescript
"nova-fiberglass-pickleball-paddle": {
  products: [
    {
      slug: "nova-fiberglass-pickleball-paddle-style-meets-performance",
      attributes: { "Style": "Style Meets Performance" }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-kiki", 
      attributes: { "Style": "Kiki" }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart-go-pop",
      attributes: { "Style": "Meowzart Go Pop" }
    },
    {
      slug: "nova-fiberglass-pickleball-paddle-meowzart",
      attributes: { "Style": "Meowzart" }
    }
  ],
  attributeTypes: ["Style", "Thickness", "Color", "Weight", "Core Technology"]
}
```

## 使用方法

### 1. 在产品详情页中使用

组件已经集成到 `ProductAttributes` 组件中，会自动在产品详情页显示。

### 2. 添加新的产品分组

1. 在 `PRODUCT_VARIANT_GROUPS` 中添加新的分组配置
2. 确保 `attributeTypes` 中的属性名与产品实际的属性名匹配
3. 为每个产品配置正确的 slug 和属性映射

### 3. 属性名匹配

- `attributeTypes` 中的属性名需要与 Saleor 中产品的实际属性名完全匹配
- 支持的属性类型包括但不限于：Style, Thickness, Color, Weight, Core Technology
- 颜色属性会自动识别并显示为颜色块

## 工作原理

1. **分组检测**: 组件根据当前产品的 slug 自动检测所属分组
2. **属性提取**: 从当前产品的变体数据和分组配置中提取所有可用属性
3. **界面生成**: 为每个属性类型生成选择界面
4. **匹配查找**: 用户选择属性后，自动查找匹配的产品
5. **页面跳转**: 找到匹配产品后自动跳转到对应详情页

## 注意事项

1. **属性名匹配**: 确保配置中的属性名与 Saleor 中的属性名完全一致
2. **slug 准确性**: 确保配置中的 slug 与实际产品页面路径一致
3. **属性完整性**: 每个产品的属性配置应该完整，包含所有相关属性
4. **翻译支持**: 属性名会自动使用翻译系统，确保翻译文件中包含对应的键值

## 扩展功能

- 支持颜色属性的特殊显示（颜色块）
- 支持属性的可用性检查
- 支持多语言翻译
- 支持自定义样式和布局
